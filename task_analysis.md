# Task Analysis: Distributed AI Inference Scheduling

This document provides a detailed breakdown of the "Distributed Task Scheduling for Edge Cluster AI Inference" problem.

## 1. High-Level Objective

The primary goal is to design a scheduling algorithm that assigns AI inference requests from multiple users to a cluster of heterogeneous servers. The objective is to **maximize a score** by processing all user samples within their requested time windows while minimizing request migrations between NPUs.

## 2. System Components

### 2.1. Servers (The Cluster)
- There are **N** types of servers (1 ≤ N ≤ 10).
- For each server type `i`:
    - **`g_i`**: Number of NPUs (1 ≤ `g_i` ≤ 10). All NPUs on a given server are identical.
    - **`k_i`**: NPU inference speed coefficient (1 ≤ `k_i` ≤ 5). Higher is faster.
    - **`m_i`**: VRAM size per NPU (1000 ≤ `m_i` ≤ 2000 MB).

### 2.2. Users (The Clients)
- There are **M** users (1 ≤ M ≤ 500).
- For each user `i`:
    - **`[s_i, e_i)`**: The time window (in milliseconds) during which they want their task completed (0 ≤ `s_i` < `e_i` ≤ 60000).
    - **`cnt_i`**: Total number of samples to be processed (1 ≤ `cnt_i` ≤ 6000).

### 2.3. AI Model & Requests
- A single, identical AI model is used for all requests.
- The model is pre-loaded on every NPU, so there is no model loading time.
- Users break their `cnt_i` samples into smaller requests, each with a specific `batchsize`.

## 3. The Scheduling Process (Lifecycle of a Request)

The simulation runs in discrete steps of 1 millisecond.

### 3.1. User Sends a Request
- A user `i` can start sending requests at or after their start time `s_i`.
- A user can only send **one request at a time**.
- If a user sends a request to server `j` at time `t_send`, the user **cannot send their next request until `t_send + latency_{j,i} + 1`**. This is a critical constraint that creates a user-specific cooldown based on their last server choice.

### 3.2. Communication & Arrival
- A request sent by user `j` to server `i` at `t_send` has a communication latency of `latency_{i,j}` (10 ≤ `latency_{i,j}` ≤ 20).
- The request arrives at the target server at `t_arrival = t_send + latency_{i,j}`.

### 3.3. NPU Queueing & Resource Allocation
Each NPU has its own request queue. Every millisecond, each NPU performs the following 4-step process in strict order:

1.  **Remove Completed Requests:** Any request that has finished its inference is removed from the NPU. Resources (VRAM) are freed.
2.  **Add New Requests:** Requests that have just arrived at the server at the current millisecond are added to the appropriate NPU's queue.
3.  **Sort Queue:** The entire queue is re-sorted based on two keys:
    - **Primary Key:** Request arrival time (earliest first).
    - **Secondary Key:** User ID (smallest first).
4.  **Allocate Resources for Inference:** The NPU scans its sorted queue from head to tail. For each request, it checks if allocating its required VRAM will exceed the NPU's total VRAM (`m_i`).
    - If it fits (`current_vram_usage + request_vram <= m_i`), the NPU commits to processing this request. The VRAM is marked as used.
    - This implies an NPU can process **multiple requests concurrently** if their combined VRAM fits.

### 3.4. Inference Execution
- A request with `batchsize` `B` on an NPU with speed `k` takes `ceil(sqrt(B) / k)` milliseconds to complete.
- The clock for inference time starts from the millisecond the NPU allocates resources to it.

## 4. Key Formulas and Calculations

- **VRAM Usage per Request:** `VRAM = a * B + b`
  - `B`: batchsize of the request.
  - `a`, `b`: Given constants (10 ≤ `a` ≤ 20, 100 ≤ `b` ≤ 200).
  - This must satisfy `a * B + b ≤ m_server`.

- **Inference Time per Request:** `Time = ceil(sqrt(B) / k)`
  - `B`: batchsize.
  - `k`: NPU speed coefficient.

- **Migration Cost (`move_i`):**
  - This is the number of times user `i`'s consecutive requests are sent to different NPUs.
  - Example: If a user sends requests to NPU_1, NPU_1, NPU_2, NPU_1, the `move` count is 2.

## 5. Constraints and Rules

- A user's requests must be sent at strictly increasing timestamps.
- The sum of batchsizes for all of a user's requests must equal their total sample count (`cnt_i`).
- The chosen NPU index for a server must be valid (1 to `g_i`).
- The chosen server index must be valid (1 to `N`).
- A user cannot send a request before their start time `s_i`.
- The number of scheduled chunks per user (`T_i`) must be between 1 and 300.
- `5 * cnt_i ≤ e_i - s_i`: A hint that sufficient time is allocated, suggesting that a simple, non-parallelized approach might be too slow.

## 6. Input/Output Format

### Input
1.  Line 1: `N` (server types).
2.  Next `N` lines: `g_i, k_i, m_i` (NPUs, speed, VRAM for each server type).
3.  Next line: `M` (users).
4.  Next `M` lines: `s_i, e_i, cnt_i` (start, end, samples for each user).
5.  Next `N` lines: `latency_matrix[i][j]` (latency from user `j` to server `i`).
6.  Last line: `a, b` (VRAM formula constants).

### Output
- For each user `i`, output two blocks of lines:
    1.  A line with a single integer `T_i`, the number of requests for user `i`.
    2.  A single line containing `T_i * 4` integers:
        - `time_j, server_j, NPU_j, B_j` for each of the `T_i` requests.
        - `time_j`: send time.
        - `server_j`: server index (1-based).
        - `NPU_j`: NPU index on that server (1-based).
        - `B_j`: batchsize for this request.

## 7. Scoring Logic

The score is a product of penalties. To maximize the score, you must minimize the penalties.

`Score = h(K) * (SUM over all users i: Score_i) * 10,000`
`Score_i = h((end_i - e_i) / (e_i - s_i)) * p(move_i)`

- **Penalty Functions:**
  - `h(x) = 2^(-x / 100)`: Penalizes lateness and the number of late users.
  - `p(x) = 2^(-x / 200)`: Penalizes migrations.

- **Key Terms:**
  - `end_i`: The millisecond when the last sample for user `i` finishes processing.
  - `K`: The total number of users who finish late (`end_i > e_i`).
  - `(end_i - e_i)`: The lateness for user `i`. If `end_i <= e_i`, this term is ≤ 0, making the `h()` factor for that user close to 1 (good).
  - `move_i`: The migration count for user `i`.

## 8. Core Challenge & Trade-offs

The central challenge is balancing several conflicting factors:

1.  **Batch Size (`B`):**
    - **Large `B`:** More efficient inference time (due to `sqrt(B)`), fewer requests needed. However, consumes more VRAM, potentially blocking other requests on the same NPU, and increases risk of exceeding memory limits.
    - **Small `B`:** Less efficient, requires more requests (and thus more time spent on latency cooldowns). However, uses less VRAM, allowing for better NPU concurrency.

2.  **Migration (`move_i`) vs. Load Balancing:**
    - **Sticking to one NPU:** Minimizes `move_i` penalty. However, this NPU can become a bottleneck, causing long queue times and making you miss the `e_i` deadline.
    - **Distributing load:** Can significantly speed up processing. However, it incurs a `move_i` penalty for every switch.

3.  **Server Choice:**
    - **Fast Servers (high `k`):** Reduce inference time.
    - **High-VRAM Servers (high `m`):** Allow for larger batches or more concurrent tasks.
    - **High-NPU Count Servers (high `g`):** Offer more parallel processing locations.
    - These choices are constrained by `latency`, which directly impacts how quickly a user can send their *next* request. Choosing a high-latency server slows down the user.

4.  **Urgency:**
    - Users with tight `e_i - s_i` windows are higher priority. The algorithm must be able to identify and prioritize them to avoid the heavy `h(K)` penalty. 