#include <iostream>
#include <vector>
#include <cmath>
#include <algorithm>
#include <numeric>
#include <set>
#include <unordered_map>

// 🚀 超级优化算法：精确数学建模 + 高效数据结构 + 智能调度策略
// 优化目标：最大化 Score = h(K) × ∏p(movei) × 10000
const double BASE_MIGRATION_PENALTY_SCORE = 200.0; // 增强迁移惩罚权重
const int INF = 1e9;
const double H_DECAY_FACTOR = 100.0;  // h(x) = 2^(-x/100)
const double P_DECAY_FACTOR = 200.0;  // p(x) = 2^(-x/200)
const double EPS = 1e-9;

// Core data structures remain the same, but max_b will now be updated per-user
struct Server {
    int id, g, k, m, max_b;
};

struct User {
    int id, s, e, cnt;
    struct Request {
        int time, server_id, npu_id_on_server, batch_size;
    };
    std::vector<Request> solution;
};

// Per-user memory model parameters (a_i, b_i)
std::vector<int> aParam; // slope per user
std::vector<int> bParam; // intercept per user

// 🚀 超级优化的NPU状态管理：高效数据结构 + 精确亲和性跟踪
struct NPUState {
    int global_id, server_type_id, server_id, npu_id_on_server;

    // 🚀 优化：使用set管理忙碌时间段，自动排序和合并
    std::set<std::pair<int, int>> busy_intervals;

    // 🚀 增强的亲和性跟踪系统
    int last_user_id;           // 最后使用此NPU的用户ID
    int consecutive_requests;   // 连续请求数（同一用户）
    int total_requests;         // 总请求数
    std::unordered_map<int, int> user_request_counts; // 每个用户的请求计数

    // 🚀 性能指标跟踪
    double avg_utilization;     // 平均利用率
    int last_finish_time;       // 最后完成时间

    NPUState() : last_user_id(-1), consecutive_requests(0), total_requests(0),
                 avg_utilization(0.0), last_finish_time(0) {}

    // 🚀 优化：高效查找最早空闲时间
    int find_earliest_free_time(int arrival_time) const {
        int free_time = arrival_time;
        // Optimized scan: break once we reach an interval that starts after free_time
        for (const auto& interval : busy_intervals) {
            if (free_time < interval.first) {
                // Since intervals are ordered by start time, no later interval can overlap
                break;
            }
            if (free_time < interval.second) {
                free_time = interval.second;
            }
        }
        return free_time;
    }

    // 🚀 优化：智能添加忙碌时段并自动合并重叠区间
    void add_busy_interval(int start, int end, int user_id = -1) {
        // 插入新区间到set中（自动排序）
        busy_intervals.insert({start, end});

        // 🚀 合并重叠区间以优化内存和查询效率
        merge_overlapping_intervals();

        // 🚀 更新增强的亲和性跟踪
        if (user_id != -1) {
            if (last_user_id == user_id) {
                consecutive_requests++;
            } else {
                last_user_id = user_id;
                consecutive_requests = 1;
            }
            total_requests++;
            user_request_counts[user_id]++;

            // 更新性能指标
            last_finish_time = std::max(last_finish_time, end);
            update_utilization_metrics();
        }
    }

private:
    // 🚀 新增：合并重叠的忙碌时间段
    void merge_overlapping_intervals() {
        if (busy_intervals.size() <= 1) return;

        std::vector<std::pair<int, int>> merged;
        for (const auto& interval : busy_intervals) {
            if (merged.empty() || merged.back().second < interval.first) {
                merged.push_back(interval);
            } else {
                merged.back().second = std::max(merged.back().second, interval.second);
            }
        }

        busy_intervals.clear();
        for (const auto& interval : merged) {
            busy_intervals.insert(interval);
        }
    }

    // 🚀 新增：更新利用率指标
    void update_utilization_metrics() {
        if (total_requests > 0) {
            int total_busy_time = 0;
            for (const auto& interval : busy_intervals) {
                total_busy_time += interval.second - interval.first;
            }
            avg_utilization = (double)total_busy_time / std::max(1, last_finish_time);
        }
    }

public:

    // 🚀 超级优化的亲和性奖励：精确数学建模
    double get_affinity_bonus_score(int user_id) const {
        if (last_user_id == user_id && consecutive_requests > 0) {
            // 基于连续请求数的亲和性奖励，模拟p(move_i)的正面影响
            double continuity_factor = 1.0 - exp(-consecutive_requests / 8.0);
            return 80.0 * continuity_factor; // 增强奖励权重
        }

        // 检查历史亲和性
        auto it = user_request_counts.find(user_id);
        if (it != user_request_counts.end() && it->second > 0) {
            double history_factor = 1.0 - exp(-it->second / 15.0);
            return 30.0 * history_factor; // 历史亲和性奖励
        }

        return 0.0;
    }

    // 🚀 智能负载均衡评分：考虑利用率和分布
    double get_load_factor_score() const {
        // 基于总请求数和利用率的综合评分
        double load_penalty = total_requests * 0.3;
        double utilization_bonus = (1.0 - avg_utilization) * 40.0; // 奖励低利用率NPU
        return utilization_bonus - load_penalty;
    }

    // 🚀 新增：NPU效率预估 (samples per millisecond for a given batch size and server k)
    double estimate_efficiency(int batch_size, int server_k) const {
        if (batch_size <= 0) return 0.0;
        // Inference time is ceil(sqrt(B)/k). Speed is B / time.
        double inference_time_estimate = ceil(sqrt(batch_size) / server_k);
        if (inference_time_estimate == 0) return 0.0;
        return (double)batch_size / inference_time_estimate; // Throughput in samples/ms
    }
};

// Global variables
std::vector<Server> servers;
std::vector<User> users;
std::vector<std::vector<int>> latencies;
// Removed global a, b (now per-user)
std::vector<NPUState> npu_states;
int inference_time_cache[6][1001]; // [k][batch_size] -> inference_duration

// 🚀 数学建模的用户排序：精确优化h(K)和h(completion_ratio)
bool compareUsers(const User& u1, const User& u2) {
    int window1 = u1.e - u1.s;
    int window2 = u2.e - u2.s;

    // 🎯 High-level Timeout Risk Assessment (prioritize K=0)
    // Estimate minimum number of requests based on max batch size
    int min_requests1 = (u1.cnt + (servers[0].max_b - 1)) / servers[0].max_b; // Assuming uniform max_b for estimation, could be refined
    int min_requests2 = (u2.cnt + (servers[0].max_b - 1)) / servers[0].max_b;

    // A rough estimate of total time needed: (min_requests * (min_latency + avg_inference_time_per_request))
    // Simplification: Assume average latency and a minimal inference duration (e.g., 5ms) + 1ms send interval.
    int estimated_time1 = u1.s + min_requests1 * (10 + 5 + 1); // min latency 10, approx 5ms per inference, 1ms send gap
    int estimated_time2 = u2.s + min_requests2 * (10 + 5 + 1);

    bool timeout_risk1 = estimated_time1 > u1.e;
    bool timeout_risk2 = estimated_time2 > u2.e;

    // 🚀 Absolute priority: Users at direct risk of missing deadline (critical for K term)
    if (timeout_risk1 != timeout_risk2) {
        return timeout_risk1; // User with timeout risk comes first
    }

    // 🎯 Advanced Priority: Based on h(x)=2^(-x/100) (or exp(-x/100)) for completion ratio
    // If both have or don't have timeout risk, optimize for h((end_i-e_i)/(e_i-s_i))
    if (!timeout_risk1 && !timeout_risk2) {
        double expected_delay_ratio1 = (window1 > 0) ? std::max(0.0, (double)(estimated_time1 - u1.e) / window1) : 0.0;
        double expected_delay_ratio2 = (window2 > 0) ? std::max(0.0, (double)(estimated_time2 - u2.e) / window2) : 0.0;

        double h_score1 = exp(-expected_delay_ratio1 / H_DECAY_FACTOR);
        double h_score2 = exp(-expected_delay_ratio2 / H_DECAY_FACTOR);

        if (std::abs(h_score1 - h_score2) > 1e-9) { // Compare with a small epsilon for doubles
            return h_score1 > h_score2; // Prioritize users with higher h_score (less projected delay)
        }

        // If h_scores are similar, consider sample density (more urgent tasks)
        double density1 = (window1 > 0) ? (double)u1.cnt / window1 : (double)u1.cnt;
        double density2 = (window2 > 0) ? (double)u2.cnt / window2 : (double)u2.cnt;
        if (std::abs(density1 - density2) > 1e-9) {
            return density1 > density2; // Higher density first
        }
    }

    // 🎯 Secondary priority: Deadline and Start Time (as tie-breakers)
    if (u1.e != u2.e) return u1.e < u2.e; // Earlier deadline first
    return u1.s < u2.s; // Earlier start time first
}

// 🚀 超级数学优化批次策略：精确建模⌈√B/k⌉最优化
std::vector<int> get_optimal_batch_candidates(int remaining_samples, int max_b, int remaining_time, bool is_urgent, int user_window, int server_k) {
    std::vector<int> candidates;
    std::set<int> unique_candidates; // 使用set自动去重

    if (remaining_samples <= 0 || max_b <= 0) return candidates;

    int max_possible = std::min(remaining_samples, max_b);
    double time_pressure = (remaining_time > 0 && user_window > 0) ? (double)remaining_time / user_window : 0.0;

    // 🚀 数学最优批次大小：基于⌈√B/k⌉的最小值点
    // 对于给定的k，最优批次大小在k²×n²附近，其中n为正整数
    std::vector<int> mathematical_optimal;
    for (int n = 1; n <= 10; ++n) {
        int optimal_size = server_k * server_k * n * n;
        if (optimal_size <= max_possible) {
            mathematical_optimal.push_back(optimal_size);
        }
        // 也考虑稍微偏离最优点的值
        if (optimal_size - 1 > 0 && optimal_size - 1 <= max_possible) {
            mathematical_optimal.push_back(optimal_size - 1);
        }
        if (optimal_size + 1 <= max_possible) {
            mathematical_optimal.push_back(optimal_size + 1);
        }
    }

    // 🚀 智能策略选择
    if (is_urgent || time_pressure < 0.2) {
        // 极度紧急：优先考虑能快速完成的批次
        unique_candidates.insert(1); // 最小批次保证进度

        // 添加数学最优解中的较小值
        for (int size : mathematical_optimal) {
            if (size <= max_possible / 2) {
                unique_candidates.insert(size);
            }
        }

        // 添加一些小批次选项
        for (int div = 2; div <= 8; div *= 2) {
            if (max_possible / div > 0) {
                unique_candidates.insert(max_possible / div);
            }
        }

    } else if (time_pressure < 0.5) {
        // 中等紧急：平衡效率和速度
        for (int size : mathematical_optimal) {
            unique_candidates.insert(size);
        }

        // 添加一些比例批次
        unique_candidates.insert(max_possible * 3 / 4);
        unique_candidates.insert(max_possible / 2);
        unique_candidates.insert(max_possible / 3);

    } else {
        // 时间充裕：优先大批次提高效率
        unique_candidates.insert(max_possible); // 最大批次

        // 添加数学最优解
        for (int size : mathematical_optimal) {
            unique_candidates.insert(size);
        }

        // 添加大批次选项
        unique_candidates.insert(max_possible * 7 / 8);
        unique_candidates.insert(max_possible * 3 / 4);
    }

    // 🚀 特殊处理：确保能完整处理剩余样本
    if (remaining_samples <= max_b) {
        unique_candidates.insert(remaining_samples); // 精确匹配剩余样本
    }

    // 🚀 智能批次大小微调：避免ceiling效应的负面影响
    std::vector<int> refined_candidates;
    for (int size : unique_candidates) {
        if (size > 0 && size <= max_possible) {
            refined_candidates.push_back(size);

            // 对于接近ceiling边界的值，尝试微调
            double sqrt_val = sqrt(size) / server_k;
            double ceiling_val = ceil(sqrt_val);
            if (abs(sqrt_val - ceiling_val) < 0.1) { // 接近ceiling边界
                // 尝试稍微减小批次大小以获得更好的ceiling效果
                int adjusted = size - 1;
                if (adjusted > 0 && adjusted <= max_possible) {
                    refined_candidates.push_back(adjusted);
                }
            }
        }
    }

    // 按批次大小降序排列（优先尝试大批次）
    std::sort(refined_candidates.rbegin(), refined_candidates.rend());

    // 去重并限制候选数量以提高性能
    refined_candidates.erase(std::unique(refined_candidates.begin(), refined_candidates.end()), refined_candidates.end());

    // 限制候选数量，避免过度搜索
    int max_candidates = is_urgent ? 8 : 12;
    if (refined_candidates.size() > max_candidates) {
        refined_candidates.resize(max_candidates);
    }

    return refined_candidates;
}

// 🚀 超级精确数学建模评估函数：完美优化评分公式 Score = h(K) × ∏p(movei) × 10000
void evaluate_choice(
    int b_size, const NPUState& npu, const User& user, int earliest_send_time, int last_npu_global_id,
    double& best_eval_score, int& best_npu_global_id, int& best_batch_size
) {
    if (b_size <= 0) return;

    int latency = latencies[npu.server_type_id][user.id];
    int arrival_time = earliest_send_time + latency;
    int inference_start_time = npu.find_earliest_free_time(arrival_time);
    int inference_duration = inference_time_cache[servers[npu.server_type_id].k][b_size];
    int finish_time = inference_start_time + inference_duration;

    double current_eval_score = 0.0;

    // 🎯 组件1：超时预防 (K项) - 最高优先级
    // 精确建模 h(K) = 2^(-K/100)，K为超时用户数
    if (finish_time > user.e) {
        // 超时的严重指数惩罚，模拟K>0时h(K)的急剧下降
        int overtime = finish_time - user.e;
        int time_window = std::max(1, user.e - user.s);
        double timeout_severity = (double)overtime / time_window;

        // 使用更精确的指数惩罚模型
        double timeout_penalty = 200000.0 * (1.0 - pow(2.0, -timeout_severity * 100.0 / H_DECAY_FACTOR));
        current_eval_score -= timeout_penalty;

        // 额外的线性惩罚，确保强烈避免超时
        current_eval_score -= overtime * 10.0;

    } else {
        // 🎯 组件2：完成比率优化 h((end_i-e_i)/(e_i-s_i))
        int time_window = std::max(1, user.e - user.s);
        double completion_ratio = (double)(user.e - finish_time) / time_window; // 正值表示提前完成

        // 精确建模 h(x) = 2^(-x/100)，这里x是延迟比率（负的completion_ratio）
        double h_value = pow(2.0, completion_ratio * 100.0 / H_DECAY_FACTOR);
        current_eval_score += h_value * 8000.0; // 增强奖励权重

        // 🚀 提前完成的额外奖励（线性）
        if (completion_ratio > 0) {
            current_eval_score += completion_ratio * time_window * 0.2;
        }
    }

    // 🎯 组件3：迁移成本优化 p(move_i) = 2^(-move_i/200)
    bool is_migration = (last_npu_global_id != -1 && npu.global_id != last_npu_global_id);
    if (is_migration) {
        // 精确建模迁移惩罚，基于p(move_i)公式
        double migration_penalty = BASE_MIGRATION_PENALTY_SCORE *
                                 (1.0 - pow(2.0, -1.0 / P_DECAY_FACTOR * 200.0));
        current_eval_score -= migration_penalty;
    } else if (last_npu_global_id == npu.global_id) {
        // 继续使用同一NPU的奖励
        current_eval_score += BASE_MIGRATION_PENALTY_SCORE * 0.9;
    }

    // 🚀 高级性能优化组件

    // 亲和性奖励（减少未来迁移的可能性）
    current_eval_score += npu.get_affinity_bonus_score(user.id);

    // 负载均衡奖励
    current_eval_score += npu.get_load_factor_score();

    // 🚀 批次效率奖励：基于数学最优性
    double batch_efficiency = npu.estimate_efficiency(b_size, servers[npu.server_type_id].k);
    current_eval_score += batch_efficiency * 8.0; // 增强权重

    // 🚀 数学最优批次大小奖励
    int server_k = servers[npu.server_type_id].k;
    double sqrt_ratio = sqrt(b_size) / server_k;
    double ceiling_waste = ceil(sqrt_ratio) - sqrt_ratio;
    current_eval_score += (1.0 - ceiling_waste) * 50.0; // 奖励接近最优的批次大小

    // 🚀 通信延迟惩罚
    current_eval_score -= latency * 1.0;

    // 🚀 时间紧迫性调整
    int remaining_time = user.e - earliest_send_time;
    if (remaining_time > 0) {
        double urgency_factor = 1.0 / (1.0 + remaining_time / 1000.0);
        current_eval_score += urgency_factor * 100.0; // 奖励在紧急情况下的快速处理
    }

    // 🚀 NPU利用率优化
    if (npu.avg_utilization < 0.8) { // 奖励使用低利用率的NPU
        current_eval_score += (0.8 - npu.avg_utilization) * 60.0;
    }

    // 更新最佳选择
    if (-current_eval_score < best_eval_score) {
        best_eval_score = -current_eval_score;
        best_npu_global_id = npu.global_id;
        best_batch_size = b_size;
    }
}

// 🚀 超级智能NPU选择：基于多维度评估的精确筛选
std::vector<int> get_candidate_npus(const User& user, int remaining_samples, int remaining_time) {
    std::vector<std::pair<double, int>> npu_scores;

    // 🎯 Dynamic candidate quantity: Adjust based on time pressure
    double time_pressure = (user.e - user.s > 0) ? (double)remaining_time / (user.e - user.s) : 1.0;
    // Select a higher ratio of candidates if highly urgent
    double candidate_ratio = time_pressure < 0.3 ? 0.7 : (time_pressure < 0.6 ? 0.5 : 0.3); // More candidates when urgent

    for (int i = 0; i < npu_states.size(); ++i) {
        const auto& npu = npu_states[i];
        int max_b = servers[npu.server_type_id].max_b;

        if (max_b <= 0) continue; // Skip NPU if it can't process any batch

        double score = 0;

        // 🎯 Performance score (base capability)
        int server_k = servers[npu.server_type_id].k;
        score += server_k * server_k * 10.0; // Quadratic reward for inference speed
        score += max_b * 0.5; // Reward for higher batch capacity

        // 🎯 Load balancing score
        score += npu.get_load_factor_score();

        // 🎯 Affinity score (reduces migration)
        score += npu.get_affinity_bonus_score(user.id);

        // 🎯 Communication latency score
        int latency = latencies[npu.server_type_id][user.id];
        score -= latency * 1.0; // Direct penalty for latency

        // 🚀 Time constraint scoring: Estimate if it can complete on time
        int estimated_batch = std::min(remaining_samples, max_b);
        if (estimated_batch > 0) {
            int estimated_inference_time = inference_time_cache[server_k][estimated_batch];
            int estimated_finish = user.s + latency + estimated_inference_time; // Simplified
            if (estimated_finish <= user.e) {
                score += 50.0; // Reward for being able to complete on time
            } else {
                score -= (estimated_finish - user.e) * 0.1; // Minor penalty for potential delay
            }
        }

        npu_scores.push_back({score, i});
    }

    // 🎯 Intelligent sorting and filtering
    std::sort(npu_scores.rbegin(), npu_scores.rend()); // Sort descending by score

    std::vector<int> candidates;
    int num_candidates_to_take = std::max(1, (int)(npu_scores.size() * candidate_ratio));

    // Add top N NPUs
    for (int i = 0; i < std::min(num_candidates_to_take, (int)npu_scores.size()); ++i) {
        candidates.push_back(npu_scores[i].second);
    }

    // 🚀 Ensure that the NPU from the last request (if any) is considered if it's not already in candidates
    // This prioritizes maintaining affinity.
    // If the NPU with affinity is not in the top N, add it.
    bool found_last_npu = false;
    for (int npu_idx : candidates) {
        if (npu_states[npu_idx].last_user_id == user.id) {
            found_last_npu = true;
            break;
        }
    }
    // If the last NPU used by this user isn't among the top candidates, add it if it's valid.
    if (!found_last_npu && user.solution.size() > 0) {
        int last_used_npu_id = user.solution.back().npu_id_on_server - 1; // Assuming npu_id_on_server is 1-indexed
        int last_used_server_id = user.solution.back().server_id - 1; // Server id is 1-indexed
        
        // Find the global ID of this NPU
        for(int i = 0; i < npu_states.size(); ++i) {
            if (npu_states[i].server_type_id == last_used_server_id && npu_states[i].npu_id_on_server == last_used_npu_id + 1) {
                candidates.push_back(npu_states[i].global_id);
                break;
            }
        }
    }
    
    // Re-sort and unique the final candidates
    std::sort(candidates.begin(), candidates.end());
    candidates.erase(std::unique(candidates.begin(), candidates.end()), candidates.end());

    return candidates;
}

int main() {
    std::ios_base::sync_with_stdio(false);
    std::cin.tie(NULL);

    int N, M;
    std::cin >> N;
    servers.resize(N);
    for (int i = 0; i < N; ++i) {
        servers[i].id = i + 1;
        std::cin >> servers[i].g >> servers[i].k >> servers[i].m;
    }

    std::cin >> M;
    users.resize(M);
    for (int i = 0; i < M; ++i) {
        users[i].id = i;
        std::cin >> users[i].s >> users[i].e >> users[i].cnt;
    }

    latencies.resize(N, std::vector<int>(M));
    for (int i = 0; i < N; ++i) {
        for (int j = 0; j < M; ++j) {
            std::cin >> latencies[i][j];
        }
    }

    // Read per-user (a_i, b_i) parameters
    aParam.resize(M);
    bParam.resize(M);
    for (int i = 0; i < M; ++i) {
        std::cin >> aParam[i] >> bParam[i];
    }

    // Precompute a rough global max_b for each server (largest possible across all users) – used mainly by compareUsers()
    for (int i = 0; i < N; ++i) {
        int global_max_b = 0;
        for (int j = 0; j < M; ++j) {
            if (aParam[j] == 0) continue; // avoid div by zero (should not happen per spec)
            int tmp = (servers[i].m - bParam[j]) / aParam[j];
            if (tmp > global_max_b) global_max_b = tmp;
        }
        servers[i].max_b = std::max(1, global_max_b); // Ensure at least 1
    }

    // Initialize NPU states
    int total_npus = 0;
    for (int i = 0; i < N; ++i) {
        total_npus += servers[i].g;
    }
    npu_states.reserve(total_npus);

    int global_npu_id_counter = 0;
    for (int i = 0; i < N; ++i) {
        for (int j = 0; j < servers[i].g; ++j) {
            npu_states.emplace_back();
            NPUState& npu = npu_states.back();
            npu.global_id = global_npu_id_counter++;
            npu.server_type_id = i;
            npu.server_id = i + 1;
            npu.npu_id_on_server = j + 1;
        }
    }

    // Precompute inference time cache
    for (int k = 1; k <= 5; ++k) {
        for (int B = 1; B <= 1000; ++B) {
            inference_time_cache[k][B] = static_cast<int>(ceil(sqrt(B) / k));
        }
    }

    // 🚀 Enhanced user sorting
    std::vector<int> original_indices(M);
    std::iota(original_indices.begin(), original_indices.end(), 0);
    std::sort(original_indices.begin(), original_indices.end(), [&](int i, int j) {
        return compareUsers(users[i], users[j]);
    });

    // 🚀 Main scheduling loop: Deeply optimized version
    for (int user_idx : original_indices) {
        User& user = users[user_idx];

        // Recompute max_b for each server with this specific user's (a_i, b_i)
        for (int si = 0; si < servers.size(); ++si) {
            if (aParam[user.id] == 0) {
                servers[si].max_b = 0; // Not expected per problem constraints
            } else {
                servers[si].max_b = (servers[si].m - bParam[user.id]) / aParam[user.id];
                if (servers[si].max_b < 0) servers[si].max_b = 0;
            }
        }

        int remaining_samples = user.cnt;
        int earliest_send_time = user.s;
        int last_npu_global_id = -1; // To track NPU for migration penalty/reward

        while (remaining_samples > 0) {
            int best_npu_global_id = -1;
            int best_batch_size = 0;
            double best_eval_score_negated = INF; // We want to minimize this (which means maximizing actual score)

            // 🚀 Super intelligent urgency determination
            int remaining_time = user.e - earliest_send_time;
            int user_window = user.e - user.s;
            if (user_window <= 0) user_window = 1; // Prevent division by zero for window=0

            // Multi-dimensional urgency assessment
            double time_ratio = (double)remaining_time / user_window;
            double sample_density_current = (remaining_time > 0) ? (double)remaining_samples / remaining_time : (double)remaining_samples;

            bool is_urgent = (time_ratio < 0.2) || // Very little time left relative to original window
                           (remaining_time < 1000) || // Absolulte short time left
                           (sample_density_current > 1.0) || // Need to process more than 1 sample/ms
                           (remaining_samples > 500 && remaining_time < 3000); // Large remaining samples, tight deadline

            // 🚀 Intelligent NPU candidate screening
            std::vector<int> candidate_npus = get_candidate_npus(user, remaining_samples, remaining_time);

            if (candidate_npus.empty()) {
                // This means no NPU could be considered based on initial filtering criteria.
                // Fallback to iterating all NPUs to find any valid solution.
                for (int i = 0; i < npu_states.size(); ++i) {
                    if (servers[npu_states[i].server_type_id].max_b > 0) {
                         candidate_npus.push_back(i);
                    }
                }
                if (candidate_npus.empty()){ // Still empty, then there are no valid NPUs at all.
                    // This indicates an extreme edge case, should not happen with given constraints.
                    // Assign to first NPU with min batch size to avoid program crash.
                    best_npu_global_id = 0;
                    best_batch_size = 1;
                    // Proceed to add to solution and continue loop, hoping for a better option next time if it somehow recovers.
                    // This might lead to "Samples Not Fully Processed" or "Batchsize Exceeds Memory" error if max_b for server 0 is 0.
                    // Given constraints, this path should be rarely taken.
                    break; 
                }
            }


            for (int npu_idx : candidate_npus) {
                const auto& npu = npu_states[npu_idx];
                int max_b_for_npu = servers[npu.server_type_id].max_b;
                if (max_b_for_npu <= 0) continue; // Ensure NPU can process a batch

                // 🚀 Intelligent batch candidate generation
                int server_k = servers[npu.server_type_id].k;
                std::vector<int> batch_candidates = get_optimal_batch_candidates(
                    remaining_samples, max_b_for_npu, remaining_time, is_urgent, user_window, server_k);

                for (int batch_size : batch_candidates) {
                    // Pre-check memory constraint with per-user parameters
                    if (aParam[user.id] * batch_size + bParam[user.id] > servers[npu.server_type_id].m) {
                        continue;
                    }
                    evaluate_choice(batch_size, npu, user, earliest_send_time, last_npu_global_id,
                                    best_eval_score_negated, best_npu_global_id, best_batch_size);
                }
            }

            // Fallback for NPU and batch size if no optimal found (should be rare with intelligent selection)
            if (best_npu_global_id == -1) {
                // If no best option was chosen (e.g., all evaluations led to INF due to invalid conditions)
                // Pick the first valid NPU and assign a minimal batch size.
                for (int i = 0; i < npu_states.size(); ++i) {
                    if (servers[npu_states[i].server_type_id].max_b > 0) {
                        best_npu_global_id = i;
                        best_batch_size = std::min(remaining_samples, servers[npu_states[i].server_type_id].max_b);
                        if (best_batch_size == 0) best_batch_size = 1; // Must be at least 1
                        break;
                    }
                }
                if (best_npu_global_id == -1) { // Still no NPU found? Fatal, but as a last resort:
                    best_npu_global_id = 0; 
                    best_batch_size = 1; 
                }
            }
            
            // Apply the chosen best option
            const NPUState& best_npu = npu_states[best_npu_global_id];
            int chosen_latency = latencies[best_npu.server_type_id][user.id];

            user.solution.push_back({earliest_send_time, best_npu.server_id, best_npu.npu_id_on_server, best_batch_size});

            int arrival_time = earliest_send_time + chosen_latency;
            int inference_start_time = best_npu.find_earliest_free_time(arrival_time);
            int inference_duration = inference_time_cache[servers[best_npu.server_type_id].k][best_batch_size];

            npu_states[best_npu_global_id].add_busy_interval(inference_start_time, inference_start_time + inference_duration, user.id);

            remaining_samples -= best_batch_size;
            earliest_send_time = earliest_send_time + chosen_latency + 1; // User can send next request 1ms after server received current.
            last_npu_global_id = best_npu_global_id;
        }
    }

    // Output results
    for (int i = 0; i < M; ++i) {
        const auto& user_sol = users[i].solution;
        std::cout << user_sol.size() << "\n";
        for (size_t j = 0; j < user_sol.size(); ++j) {
            std::cout << user_sol[j].time << " "
                      << user_sol[j].server_id << " "
                      << user_sol[j].npu_id_on_server << " "
                      << user_sol[j].batch_size << (j == user_sol.size() - 1 ? "" : " ");
        }
        std::cout << "\n";
    }

    return 0;
}